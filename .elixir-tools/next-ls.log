
23:06:05.424 [info] Loading 146 CA(s) from :otp store

23:06:05.456 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

23:06:07.623 [info] Loading 146 CA(s) from :otp store

23:06:07.688 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

23:06:10.196 [info] Loading 146 CA(s) from :otp store

23:06:10.236 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

23:06:12.558 [info] Loading 146 CA(s) from :otp store

23:06:12.623 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

23:06:15.040 [info] Loading 146 CA(s) from :otp store

23:06:15.106 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

21:44:07.825 [info] Loading 146 CA(s) from :otp store

21:44:07.862 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

21:44:09.905 [info] Loading 146 CA(s) from :otp store

21:44:09.945 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

21:44:12.277 [info] Loading 146 CA(s) from :otp store

21:44:12.323 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

21:44:14.295 [info] Loading 146 CA(s) from :otp store

21:44:14.340 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3

21:44:16.319 [info] Loading 146 CA(s) from :otp store

21:44:16.344 [notice] Application next_ls exited: NextLS.Application.start(:normal, []) returned an error: shutdown: failed to start child: NextLS.LSPSupervisor
    ** (EXIT) shutdown: failed to start child: NextLS
        ** (EXIT) an exception was raised:
            ** (ErlangError) Erlang error: :enoent
                (elixir 1.17.3) lib/system.ex:1114: System.cmd("elixir", ["--short-version"], [])
                (next_ls 0.23.3) lib/next_ls.ex:47: NextLS.init/2
                (gen_lsp 0.10.0) lib/gen_lsp.ex:201: GenLSP.init/1
                (stdlib 6.2) proc_lib.erl:329: :proc_lib.init_p_do_apply/3
