defmodule BetamattLiveApp.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      BetamattLiveAppWeb.Telemetry,
      BetamattLiveApp.Repo,
      {DNSCluster, query: Application.get_env(:betamatt_live_app, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: BetamattLiveApp.PubSub},
      # Start a worker by calling: BetamattLiveApp.Worker.start_link(arg)
      # {BetamattLiveApp.Worker, arg},
      # Start to serve requests, typically the last entry
      BetamattLiveAppWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: BetamattLiveApp.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    BetamattLiveAppWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
