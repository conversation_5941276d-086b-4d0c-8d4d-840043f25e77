defmodule YourAppWeb.ImageSliderLive do
  use Phoenix.LiveView

  def mount(_params, _session, socket) do
    slides = [
      %{image: "/images/slide1.jpg", alt: "Nature 1", caption: "Beautiful Nature 1"},
      %{image: "/images/slide2.jpg", alt: "Nature 2", caption: "Beautiful Nature 2"},
      %{image: "/images/slide3.jpg", alt: "Nature 3", caption: "Beautiful Nature 3"}
    ]

    {:ok,
     socket
     |> assign(:slides, slides)
     |> assign(:current_index, 0)
     |> assign(:auto_advance, true)
     |> schedule_advance()}
  end

  def handle_event("next", _, socket) do
    next_index = rem(socket.assigns.current_index + 1, length(socket.assigns.slides))
    {:noreply,
     socket
     |> assign(:current_index, next_index)
     |> reset_auto_advance()}
  end

  def handle_event("prev", _, socket) do
    prev_index = rem(socket.assigns.current_index - 1 + length(socket.assigns.slides),
                 length(socket.assigns.slides))
    {:noreply,
     socket
     |> assign(:current_index, prev_index)
     |> reset_auto_advance()}
  end

  def handle_event("goto", %{"index" => index}, socket) do
    {:noreply,
     socket
     |> assign(:current_index, String.to_integer(index))
     |> reset_auto_advance()}
  end

  def handle_info(:advance, socket) do
    if socket.assigns.auto_advance do
      next_index = rem(socket.assigns.current_index + 1, length(socket.assigns.slides))
      {:noreply,
       socket
       |> assign(:current_index, next_index)
       |> schedule_advance()}
    else
      {:noreply, socket}
    end
  end

  defp schedule_advance(socket) do
    if socket.assigns.auto_advance do
      Process.send_after(self(), :advance, 5000) # 5 seconds
    end
    socket
  end

  defp reset_auto_advance(socket) do
    Process.cancel_timer(socket.assigns[:advance_timer])
    schedule_advance(socket)
  end
end
