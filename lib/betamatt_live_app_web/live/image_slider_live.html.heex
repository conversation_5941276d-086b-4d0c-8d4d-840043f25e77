<div class="relative max-w-3xl mx-auto">
  <!-- Slides Container -->
  <div class="relative overflow-hidden h-96">
    <%= for {slide, index} <- Enum.with_index(@slides) do %>
      <div 
        class={"absolute w-full h-full transition-opacity duration-300 #{if index == @current_index, do: "opacity-100", else: "opacity-0"}"}
        phx-hook="SlideTransition"
        data-index={index}
        data-current={@current_index}
      >
        <img src={slide.image} alt={slide.alt} class="w-full h-full object-cover" />
        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4">
          <%= slide.caption %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Navigation Arrows -->
  <button 
    phx-click="prev" 
    class="absolute left-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75"
  >
    &larr;
  </button>
  <button 
    phx-click="next" 
    class="absolute right-4 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75"
  >
    &rarr;
  </button>

  <!-- Indicators -->
  <div class="flex justify-center gap-2 mt-4">
    <%= for {_slide, index} <- Enum.with_index(@slides) do %>
      <button 
        phx-click="goto" 
        phx-value-index={index}
        class={"w-3 h-3 rounded-full #{if index == @current_index, do: "bg-gray-700", else: "bg-gray-300"}"}
      >
      </button>
    <% end %>
  </div>
</div>