<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.8/dist/cdn.min.js"></script>




<div class="mt-15"
  x-data="{
    images: [
      'https://images.unsplash.com/photo-1570174032567-7375b65d1e67?w=800&auto=format&fit=crop',
      'https://images.unsplash.com/photo-1496614932623-0a3a9743552e?q=80&w=800&auto=format&fit=crop',
      'https://images.unsplash.com/photo-1498598457418-36ef20772bb9?q=80&w=800&auto=format&fit=crop'
    ],
    currentIndex: 0,
    autoplayInterval: null,
    autoplayProgress: 0,
    startAutoplay() {
      this.stopAutoplay();
      this.autoplayInterval = setInterval(() => {
        this.next();
        this.autoplayProgress = 0;
      }, 3000);
      this.updateProgress();
    },
    stopAutoplay() {
      clearInterval(this.autoplayInterval);
      this.autoplayInterval = null;
    },
    next() {
      this.currentIndex = (this.currentIndex + 1) % this.images.length;
    },
    prev() {
      this.currentIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
    },
    updateProgress() {
      setInterval(() => {
        if (this.autoplayInterval) {
          this.autoplayProgress = (this.autoplayProgress + (100 / 30)) % 100;
        }
      }, 100);
    }
  }"
  x-init="startAutoplay()"
  class="relative w-50 overflow-hidden max-w-4xl mx-auto"
>
  <!-- Slides -->
  <div 
    class="flex transition-transform duration-500 ease-in-out"
    x-bind:style="`transform: translateX(-${currentIndex * 100}%)`"
  >
    <template x-for="(image, index) in images" x-bind:key="index">
      <div class="w-full h-64">
        <img 
          x-bind:src="image" 
          class="w-full h-64 object-cover" 
          x-bind:alt="`Slide ${index + 1}`" 
        >
      </div>
    </template>
  </div>

  qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq


</div>
